// import { utils } from "..";

// import {
//     AIMessage,
//     BaseMessage,
//     HumanMessage,
//     SystemMessage,
// } from "@langchain/core/messages";
// import { InventorySearchArgs } from "./inventory-tools";
// import "./inventory-tools";
// import { ManageCartArgs } from "./cart-tools";
// import { ChatModelNme, llmFactory } from "./llm-factory";

// export interface ChatBotMessage {
//     text: string;
//     type: "user" | "system" | "ai";
// }

// export interface SearchInventoryResponse {
//     toolName: "searchInventory";
//     text: "";
//     args: InventorySearchArgs;
// }

// export interface ManageCartResponse {
//     toolName: "manageCart";
//     text: "";
//     args: ManageCartArgs;
// }

// export type ToolResponse = SearchInventoryResponse | ManageCartResponse;

// export class ChatbotService {
//     private _llm;
//     private _messages: BaseMessage[] = [];
//     modelName: ChatModelNme;

//     constructor() {
//         this.modelName = process.env.CHATBOT as ChatModelNme;

//         this._llm = llmFactory(this.modelName);

//         // const tools = [inventorySearchTool, manageCartTool];
//         // this._llm = llm.bindTools(tools);

//         this._messages.push(
//             new SystemMessage(`
//                 You are a salesperson who is native to Persian.
//                 You are the only sales channel so do not refer anyone to a website, phone call, etc.
//                 Do not give any extra info about the online shop or the products. like returning process, products origin, etc.

//                 If the messages include questions about the online shop's product, use the data that might contain the information that you can use to find the answers.
//                 Only answer to the asked question and do not include additional information. Be right to the point.
//                 You should be able to answer the questions about the products by analyzing the data and using your general knowledge.
//                 If you can't find the product from the data say that the online shop doesn't have that item.

//                 If the messages are not about products, then answer them in Persian.

//                 Be friendly and welcoming and feel free to include emojis if necessary.

//                 Do not format texts.
//             `),
//         );
//     }

//     private _createMessage = (
//         type: ChatBotMessage["type"],
//         text: ChatBotMessage["text"],
//     ) => {
//         if (type === "system") {
//             return new SystemMessage(text);
//         } else if (type === "user") {
//             return new HumanMessage(text);
//         } else if (type === "ai") {
//             return new AIMessage(text);
//         }
//     };

//     addMessages = (messages: ChatBotMessage[]) => {
//         for (const message of messages) {
//             const createdMessage = this._createMessage(
//                 message.type,
//                 message.text,
//             );

//             if (utils.isNotNil(createdMessage)) {
//                 this._messages.push(createdMessage);
//             }
//         }
//     };

//     processMessages = async () => {
//         const response = await this._llm.invoke(this._messages);
//         const content = (response.content as string)
//             .replaceAll("`", "")
//             .replaceAll("json", "");

//         console.log(content);

//         if (content.includes("infoFa")) {
//             return [
//                 {
//                     toolName: "searchInventory",
//                     text: "",
//                     args: JSON.parse(content),
//                 },
//             ] as SearchInventoryResponse[];
//         }

//         return [
//             {
//                 toolName: null,
//                 text: content,
//                 args: [],
//             },
//         ];
//     };
// }
