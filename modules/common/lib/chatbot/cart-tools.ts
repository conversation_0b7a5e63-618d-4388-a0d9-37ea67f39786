import { z } from "zod";
import { tool } from "@langchain/core/tools";

export interface ManageCartArgs {
    id: string;
    action: "add" | "remove";
    quantity: number;
}

const manageCartSchema = z.object({
    id: z
        .union([z.string(), z.number()])
        .describe("Id of the product to add to cart or remove from the cart"),
    action: z.enum(["add", "remove"]).describe("Add or remove the product"),
    quantity: z
        .number()
        .describe("Total number of the product to add or remove"),
});

export const createModifyCartTool = (
    fn: (
        id: string,
        action: "add" | "remove",
        quantity: number,
    ) => Promise<string>,
) =>

    tool(
        async (input) => {
            // Convert id to string if it's a number
            const idStr = typeof input.id === 'number' ? input.id.toString() : input.id;
            const result = await fn(idStr, input.action, input.quantity);

            return result;
        },
        {
            name: "modifyCart",
            description:
                "Manages the user's cart. Adds product to cart or remove it from cart. This tool will return a message confirming the action was successful and showing the updated cart contents. The response from this tool will accurately reflect whether the operation was successful - NEVER contradict or question the response from this tool.",
            schema: manageCartSchema,
        },
    );

export const createCartInfoTool = (fn: () => Promise<string>) =>
    tool(
        async (_input: any) => {
            console.log("Getting cart info");
            const result = await fn();
            return result;
        },
        {
            name: "cartInfo",
            description:
                "Get detailed information about the user's CART (سبد خرید) - NOT orders (سفارش). Use this tool when users ask about their cart using words like 'سبد خرید', 'سبدم', or 'سبد خریدم'. The response from this tool will be the ACTUAL CURRENT STATE of the user's cart - if it returns that the cart is empty, it means the cart is truly empty; if it returns a list of items, those are the actual items in the cart. NEVER contradict or question the response from this tool. NEVER suggest there might be a technical issue with the cart when this tool returns information. NEVER use this tool for order inquiries.",
            schema: z.object({}).optional(),
        },
    );

export const createCardOrderConfirmTool = (
    fn: (confirm: boolean) => Promise<string>,
) =>
    tool(
        async (input: { confirm: boolean }) => {
            const result = await fn(input.confirm);
            return result;
        },
        {
            name: "confirmOrder",
            description: "Confirms or cancels the pending order in the cart",
            schema: z.object({
                confirm: z
                    .boolean()
                    .describe("True to confirm the order, false to cancel it"),
            }),
        },
    );
