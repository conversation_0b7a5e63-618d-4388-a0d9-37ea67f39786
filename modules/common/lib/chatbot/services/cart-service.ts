import { RedisCartManager } from "../../redis/redis-cart-manager";
import { RedisConnection } from "../../redis";
import { CardHelperFunctions, CART_ACTION, LANGUAGE } from "../../../base/types/typing";
import { formatPrice } from "../../utils/text-formatter";
import { getCurrentLanguage } from "../../utils/language";
import { getInsufficientStockMessage, validateStockQuantity } from "../utils/stock-validation";

export class CartService {
    private _redisCartManager: RedisCartManager;
    private _threadId: string;
    private _helpers: CardHelperFunctions;

    constructor(
        redisConnection: RedisConnection,
        threadId: string,
        helpers: CardHelperFunctions,
    ) {
        this._redisCartManager = new RedisCartManager(redisConnection);
        this._threadId = threadId;
        this._helpers = helpers;
    }

    //Add and Remove items from Cart
    async modifyCart(
        id: string,
        action: CART_ACTION,
        quantity: number,
    ): Promise<string> {
        console.log(
            `Cart action: ${action} product ${id} quantity ${quantity}`,
        );

        if (!id || quantity <= 0) {
            console.error(
                `Invalid cart modification parameters: id=${id}, quantity=${quantity}`,
            );
            const currentLanguage = getCurrentLanguage();
            return currentLanguage === LANGUAGE.ENGLISH
                ? "Error modifying your cart. Please try again."
                : "خطا در تغییر سبد خرید. لطفا مجددا تلاش کنید.";
        }

        try {
            // Get product details first if we're adding a new item
            let productDetails = null;
            if (action === CART_ACTION.ADD && this._helpers.getProductDetails) {
                try {
                    productDetails = await this._helpers.getProductDetails(id);
                    console.log(
                        `Retrieved product details for ${id}:`,
                        productDetails,
                    );

                    // Validate stock quantity if we have product details with total stock information
                    if (productDetails && typeof productDetails.total === 'number') {
                        // Get current cart to check existing quantity
                        const currentCart = await this._redisCartManager.getCart(this._threadId);
                        const existingItem = currentCart.items.find(item => String(item.id) === String(id));
                        const currentQuantityInCart = existingItem ? existingItem.amount : 0;
                        const totalRequestedQuantity = currentQuantityInCart + quantity;

                        const stockValidation = validateStockQuantity(
                            totalRequestedQuantity,
                            productDetails.total
                        );

                        if (!stockValidation.isValid) {
                            console.log(
                                `Stock validation failed: requested ${totalRequestedQuantity}, total stock ${productDetails.total}`
                            );
                            return getInsufficientStockMessage(
                                totalRequestedQuantity,
                                productDetails.total,
                                productDetails.name
                            );
                        }
                    }
                } catch (error) {
                    console.error(
                        `Error getting product details for ${id}:`,
                        error,
                    );
                }
            }

            // Use atomic update to modify the cart
            const updatedCart = await this._redisCartManager.updateCart(
                this._threadId,
                (pendingCart) => {
                    console.log(
                        "Current cart state (before):",
                        JSON.stringify(pendingCart),
                    );

                    // Find the item in the cart - use loose comparison to handle string/number differences
                    const itemIndex = pendingCart.items.findIndex(
                        (item) => String(item.id) === String(id),
                    );

                    // Handle existing item in cart
                    if (itemIndex >= 0) {
                        if (action === CART_ACTION.ADD) {
                            // Add to existing item
                            pendingCart.items[itemIndex].amount += quantity;

                            // Update product details if available and not already present
                            if (productDetails) {
                                const { name, price, attributes } =
                                    productDetails;

                                // Only update if the details are not already present
                                if (!pendingCart.items[itemIndex].name) {
                                    pendingCart.items[itemIndex].name = name;
                                }

                                if (!pendingCart.items[itemIndex].price) {
                                    pendingCart.items[itemIndex].price = price;
                                }

                                if (!pendingCart.items[itemIndex].attributes) {
                                    pendingCart.items[itemIndex].attributes =
                                        attributes;
                                }

                                console.log(
                                    `Updated product details for item ${id}`,
                                );
                            }

                            console.log(
                                `Added ${quantity} of item ${id}, new amount: ${pendingCart.items[itemIndex].amount}`,
                            );
                        } else if (action === CART_ACTION.REMOVE) {
                            // Remove from existing item
                            pendingCart.items[itemIndex].amount -= quantity;
                            console.log(
                                `Removed ${quantity} of item ${id}, new amount: ${pendingCart.items[itemIndex].amount}`,
                            );

                            // Remove item completely if amount is zero or negative
                            if (pendingCart.items[itemIndex].amount <= 0) {
                                console.log(
                                    `Removing item ${id} from cart as amount is now ${pendingCart.items[itemIndex].amount}`,
                                );
                                pendingCart.items.splice(itemIndex, 1);
                            }
                        }
                    } else if (action === CART_ACTION.ADD) {
                        // Add new item to cart with product details if available
                        if (productDetails) {
                            const { name, price, attributes } = productDetails;
                            pendingCart.items.push({
                                id,
                                amount: quantity,
                                name,
                                price,
                                attributes,
                            });
                            console.log(
                                `Added new item ${id} with details and amount ${quantity} to cart`,
                            );
                        } else {
                            // Fallback if no details are available
                            pendingCart.items.push({ id, amount: quantity });
                            console.log(
                                `Added new item ${id} with amount ${quantity} to cart (no details available)`,
                            );
                        }
                    } else {
                        console.log(
                            `Cannot remove item ${id} as it's not in the cart`,
                        );
                    }

                    // Update cart state
                    pendingCart.pendingConfirmation =
                        pendingCart.items.length > 0;

                    return pendingCart;
                },
            );

            // Log the number of items in cart
            console.log(
                `Total items in cart: ${updatedCart.items.reduce(
                    (total, item) => total + item.amount,
                    0,
                )}`,
            );

            console.log(
                "Current cart state (after):",
                JSON.stringify(updatedCart),
            );

            // Return appropriate message with more details
            if (updatedCart.items.length === 0) {
                const currentLanguage = getCurrentLanguage();
                return currentLanguage === LANGUAGE.ENGLISH
                    ? "Your shopping cart is empty."
                    : "سبد خرید شما خالی است.";
            } else {
                // Get current language
                const currentLanguage = getCurrentLanguage();

                // Calculate total price if possible
                let totalPrice = 0;
                let hasPriceInfo = false;

                // Format cart items with detailed information
                const itemsList = updatedCart.items
                    .map((item) => {
                        let itemInfo = "";

                        // Use product name if available, otherwise use ID
                        if (item.name) {
                            itemInfo += `- ${item.name}\n`;
                        } else {
                            if (currentLanguage === LANGUAGE.ENGLISH) {
                                itemInfo += `- Product ${item.id}\n`;
                            } else {
                                itemInfo += `- محصول ${item.id}\n`;
                            }
                        }

                        // Add price if available
                        if (item.price) {
                            // For English, ensure the price is stored as an integer in Redis
                            if (currentLanguage === LANGUAGE.ENGLISH) {
                                // Convert decimal price to integer by removing decimal point
                                // This ensures BigInt conversion works in the order service
                                if (item.price.includes('.')) {
                                    // Store the original price for display
                                    const displayPrice = item.price;

                                    // Remove decimal point for storage (e.g., "16.23" -> "1623")
                                    item.price = item.price.replace('.', '');

                                    console.log(`Converted price from ${displayPrice} to ${item.price} for BigInt compatibility`);
                                }
                            }

                            // Format the price for display using our utility function
                            const formattedPrice = formatPrice(item.price, currentLanguage);

                            if (currentLanguage === LANGUAGE.ENGLISH) {
                                itemInfo += `  - Price: ${formattedPrice}\n`;
                            } else {
                                itemInfo += `  - قیمت: ${formattedPrice}\n`;
                            }

                            // Try to calculate total price
                            try {
                                const priceValue = parseInt(
                                    item.price.replace(/,/g, ""),
                                );
                                if (!isNaN(priceValue)) {
                                    totalPrice += priceValue * item.amount;
                                    hasPriceInfo = true;
                                }
                            } catch (e) {
                                console.error("Error parsing price:", e);
                            }
                        }

                        // Add quantity
                        if (currentLanguage === LANGUAGE.ENGLISH) {
                            itemInfo += `  - Quantity: ${item.amount}\n`;
                        } else {
                            itemInfo += `  - تعداد: ${item.amount} عدد\n`;
                        }

                        // Add attributes if available
                        if (item.attributes && item.attributes.length > 0) {
                            item.attributes.forEach((attr) => {
                                itemInfo += `  - ${attr.key}: ${attr.value}\n`;
                            });
                        }

                        return itemInfo;
                    })
                    .join("\n");

                let response;
                if (currentLanguage === LANGUAGE.ENGLISH) {
                    response = `Product ${action === CART_ACTION.ADD ? "added to" : "removed from"} your cart.\n\n`;
                    response += `Your shopping cart:\nItems:\n${itemsList}\n`;

                    // Add total price if available
                    if (hasPriceInfo) {
                        const formattedTotalPrice = formatPrice(totalPrice, currentLanguage);
                        response += `\nTotal price: ${formattedTotalPrice}\n\n`;
                    }

                    response += `Would you like to confirm your order or continue shopping? You can also remove items from your cart.`;
                } else {
                    response = `محصول ${action === CART_ACTION.ADD ? "به سبد خرید اضافه شد" : "از سبد خرید حذف شد"}.\n\n`;
                    response += `سبد خرید شما:\nاقلام:\n${itemsList}\n`;

                    // Add total price if available
                    if (hasPriceInfo) {
                        const formattedTotalPrice = formatPrice(totalPrice, currentLanguage);
                        response += `\nمبلغ نهایی: ${formattedTotalPrice}\n\n`;
                    }

                    response += `آیا مایل به تایید سفارش هستید یا می‌خواهید به خرید ادامه دهید؟ همچنین می‌توانید محصولی را از سبد خرید حذف کنید.`;
                }

                return response;
            }
        } catch (error) {
            console.error("Error in modifyCart:", error);
            const currentLanguage = getCurrentLanguage();
            return currentLanguage === LANGUAGE.ENGLISH
                ? "Error modifying your cart. Please try again."
                : "خطا در تغییر سبد خرید. لطفا مجددا تلاش کنید.";
        }
    }

    async manageCartOrderConfirm(confirm: boolean): Promise<string> {
        console.log(`Order confirmation received: ${confirm}`);

        try {
            // If cancelling the order, just clear the cart
            if (!confirm) {
                await this._redisCartManager.clearCart(this._threadId);
                const currentLanguage = getCurrentLanguage();
                return currentLanguage === LANGUAGE.ENGLISH
                    ? "Your cart has been successfully emptied. You can now search for new products to start shopping again. If you need any specific product, I'd be happy to help!"
                    : "سبد خرید شما با موفقیت خالی شد. حالا شما می‌توانید با جستجوی محصولات جدید، دوباره خرید کنید. اگر به محصول خاصی نیاز دارید، خوشحال می‌شوم کمک کنم!";
            }

            // Get the cart without a lock since we're just reading it
            const pendingCart = await this._redisCartManager.getCart(
                this._threadId,
            );
            console.log(
                "Current cart state for order confirmation:",
                JSON.stringify(pendingCart),
            );

            // For English, ensure all prices are stored as integers in Redis
            const currentLanguage = getCurrentLanguage();
            if (currentLanguage === LANGUAGE.ENGLISH) {
                // Process each item to ensure prices are in the correct format
                for (const item of pendingCart.items) {
                    if (item.price && item.price.includes('.')) {
                        // Store the original price for display
                        const displayPrice = item.price;

                        // Remove decimal point for storage (e.g., "16.23" -> "1623")
                        item.price = item.price.replace('.', '');

                        console.log(`[manageCartOrderConfirm] Converted price from ${displayPrice} to ${item.price} for BigInt compatibility`);
                    }
                }
            }

            if (pendingCart.items.length === 0) {
                const currentLanguage = getCurrentLanguage();
                return currentLanguage === LANGUAGE.ENGLISH
                    ? "Your shopping cart is empty. Please add a product to your cart first."
                    : "سبد خرید شما خالی است. لطفا ابتدا محصولی به سبد خرید خود اضافه کنید.";
            }

            try {
                let orderId = null;
                // Process each item in the cart
                for (const item of pendingCart.items) {
                    console.log(
                        `Processing order item: ${item.id}, amount: ${item.amount}, price: ${item.price}`,
                    );

                    // Make sure the price is in the correct format for BigInt conversion
                    if (item.price && item.price.includes('.')) {
                        const displayPrice = item.price;
                        item.price = item.price.replace('.', '');
                        console.log(`[manageCartOrderConfirm] Final price conversion from ${displayPrice} to ${item.price} for BigInt compatibility`);
                    }

                    const result = await this._helpers.manageCart(
                        item.id,
                        CART_ACTION.ADD,
                        item.amount,
                    );

                    // Try to extract the order ID from the result
                    try {
                        const orderData = JSON.parse(result);
                        if (orderData && orderData.id) {
                            orderId = orderData.id;
                            console.log(`Extracted order ID: ${orderId}`);
                        }
                    } catch (parseError) {
                        console.error("Error parsing order data:", parseError);
                    }
                }

                // Clear the cart after successful order processing
                await this._redisCartManager.clearCart(this._threadId);

                const currentLanguage = getCurrentLanguage();
                if (orderId) {
                    return currentLanguage === LANGUAGE.ENGLISH
                        ? `Your order has been successfully placed! Your order number is: ${orderId}, which you can use to track your order status.`
                        : `سفارش شما با موفقیت ثبت شد! شماره سفارش شما: ${orderId} است که می‌توانید با آن وضعیت سفارش خود را پیگیری کنید.`;
                } else {
                    // Fallback if we couldn't get the order ID - use the simple message as requested
                    return currentLanguage === LANGUAGE.ENGLISH
                        ? `Sorry, there was a problem placing your order. You can search for the product again and add it to your cart.`
                        : `متاسفانه در ثبت سفارش مشکلی پیش آمد شما میتوانید با جستجوی مجدد محصول آن را به سبد خرید خود اضافه کنید`;
                }
            } catch (error) {
                console.error("Error processing order:", error);

                // Use a simple generic error message as requested
                const currentLanguage = getCurrentLanguage();
                return currentLanguage === LANGUAGE.ENGLISH
                    ? "Sorry, there was a problem placing your order. You can search for the product again and add it to your cart."
                    : "متاسفانه در ثبت سفارش مشکلی پیش آمد شما میتوانید با جستجوی مجدد محصول آن را به سبد خرید خود اضافه کنید";
            }
        } catch (cartError) {
            console.error(
                "Error retrieving cart for order confirmation:",
                cartError,
            );

            // Use the same simple message for consistency
            const currentLanguage = getCurrentLanguage();
            return currentLanguage === LANGUAGE.ENGLISH
                ? "Sorry, there was a problem placing your order. You can search for the product again and add it to your cart."
                : "متاسفانه در ثبت سفارش مشکلی پیش آمد شما میتوانید با جستجوی مجدد محصول آن را به سبد خرید خود اضافه کنید";
        }
    }

    // Helper method to update product details for all items in the cart
    async updateCartItemsDetails(): Promise<void> {
        if (!this._helpers.getProductDetails) {
            console.log(
                "getProductDetails function not available, skipping cart details update",
            );
            return;
        }

        try {
            // First get the current cart
            const currentCart = await this._redisCartManager.getCart(
                this._threadId,
            );

            // Check if we need to update any items
            const itemsNeedingUpdate = currentCart.items.filter(
                (item) => !item.name || !item.price || !item.attributes,
            );

            if (itemsNeedingUpdate.length === 0) {
                console.log("All items already have details, skipping update");
                return;
            }

            console.log(
                `Found ${itemsNeedingUpdate.length} items needing details update`,
            );

            // Get product details for each item that needs updating
            const detailsPromises = itemsNeedingUpdate.map(async (item) => {
                try {
                    const details = await this._helpers.getProductDetails!(
                        item.id,
                    );
                    return {
                        id: item.id,
                        details,
                    };
                } catch (error) {
                    console.error(
                        `Error getting details for item ${item.id}:`,
                        error,
                    );
                    return {
                        id: item.id,
                        details: null,
                    };
                }
            });

            const itemDetails = await Promise.all(detailsPromises);

            // Now update the cart with the new details
            await this._redisCartManager.updateCart(
                this._threadId,
                (pendingCart) => {
                    console.log("Updating product details for items in cart");

                    // Update each item with its details
                    for (const { id, details } of itemDetails) {
                        if (!details) continue;

                        const itemIndex = pendingCart.items.findIndex(
                            (item) => item.id === id,
                        );
                        if (itemIndex === -1) continue;

                        const { name, price, attributes } = details;

                        // Update the item in the cart
                        if (!pendingCart.items[itemIndex].name) {
                            pendingCart.items[itemIndex].name = name;
                        }

                        if (!pendingCart.items[itemIndex].price) {
                            // For English, ensure the price is stored as an integer in Redis
                            const currentLanguage = getCurrentLanguage();
                            if (currentLanguage === LANGUAGE.ENGLISH && price && price.includes('.')) {
                                // Convert decimal price to integer by removing decimal point
                                // This ensures BigInt conversion works in the order service
                                const displayPrice = price;
                                const integerPrice = price.replace('.', '');
                                console.log(`Converted price from ${displayPrice} to ${integerPrice} for BigInt compatibility`);
                                pendingCart.items[itemIndex].price = integerPrice;
                            } else {
                                pendingCart.items[itemIndex].price = price;
                            }
                        }

                        if (!pendingCart.items[itemIndex].attributes) {
                            pendingCart.items[itemIndex].attributes =
                                attributes;
                        }

                        console.log(`Updated details for item ${id} in cart`);
                    }

                    return pendingCart;
                },
            );
        } catch (error) {
            console.error("Error updating cart item details:", error);
        }
    }

    async getCartInfo(): Promise<string> {
        console.log("Getting cart info");

        try {
            // Try to update any missing product details first
            await this.updateCartItemsDetails();

            // Now get the cart with updated details
            const pendingCart = await this._redisCartManager.getCart(
                this._threadId,
            );
            console.log(
                "Current cart state for info:",
                JSON.stringify(pendingCart),
            );

            if (pendingCart.items.length > 0) {
                // Get current language
                const currentLanguage = getCurrentLanguage();

                // Calculate total price if possible
                let totalPrice = 0;
                let hasPriceInfo = false;

                // Format cart items with detailed information
                const itemsList = pendingCart.items
                    .map((item) => {
                        let itemInfo = "";

                        // Use product name if available, otherwise use ID
                        if (item.name) {
                            itemInfo += `- ${item.name}\n`;
                        } else {
                            if (currentLanguage === LANGUAGE.ENGLISH) {
                                itemInfo += `- Product ${item.id}\n`;
                            } else {
                                itemInfo += `- محصول ${item.id}\n`;
                            }
                        }

                        // Add price if available
                        if (item.price) {
                            // For English, ensure the price is stored as an integer in Redis
                            if (currentLanguage === LANGUAGE.ENGLISH) {
                                // Convert decimal price to integer by removing decimal point
                                // This ensures BigInt conversion works in the order service
                                if (item.price.includes('.')) {
                                    // Store the original price for display
                                    const displayPrice = item.price;

                                    // Remove decimal point for storage (e.g., "16.23" -> "1623")
                                    item.price = item.price.replace('.', '');

                                    console.log(`Converted price from ${displayPrice} to ${item.price} for BigInt compatibility`);
                                }
                            }

                            // Format the price for display using our utility function
                            const formattedPrice = formatPrice(item.price, currentLanguage);

                            if (currentLanguage === LANGUAGE.ENGLISH) {
                                itemInfo += `  - Price: ${formattedPrice}\n`;
                            } else {
                                itemInfo += `  - قیمت: ${formattedPrice}\n`;
                            }

                            // Try to calculate total price
                            try {
                                const priceValue = parseInt(
                                    item.price.replace(/,/g, ""),
                                );
                                if (!isNaN(priceValue)) {
                                    totalPrice += priceValue * item.amount;
                                    hasPriceInfo = true;
                                }
                            } catch (e) {
                                console.error("Error parsing price:", e);
                            }
                        }

                        // Add quantity
                        if (currentLanguage === LANGUAGE.ENGLISH) {
                            itemInfo += `  - Quantity: ${item.amount}\n`;
                        } else {
                            itemInfo += `  - تعداد: ${item.amount} عدد\n`;
                        }

                        // Add attributes if available
                        if (item.attributes && item.attributes.length > 0) {
                            item.attributes.forEach((attr) => {
                                itemInfo += `  - ${attr.key}: ${attr.value}\n`;
                            });
                        }

                        return itemInfo;
                    })
                    .join("\n");

                let response;
                if (currentLanguage === LANGUAGE.ENGLISH) {
                    response = `Your shopping cart:\nItems:\n${itemsList}\n`;

                    // Add total price if available
                    if (hasPriceInfo) {
                        const formattedTotalPrice = formatPrice(totalPrice, currentLanguage);
                        response += `\nTotal price: ${formattedTotalPrice}\n\n`;
                    }

                    response += `Would you like to confirm your order or continue shopping? You can also remove items from your cart.`;
                } else {
                    response = `سبد خرید شما:\nاقلام:\n${itemsList}\n`;

                    // Add total price if available
                    if (hasPriceInfo) {
                        const formattedTotalPrice = formatPrice(totalPrice, currentLanguage);
                        response += `\nمبلغ نهایی: ${formattedTotalPrice}\n\n`;
                    }

                    response += `آیا مایل به تایید سفارش هستید یا می‌خواهید به خرید ادامه دهید؟ همچنین می‌توانید محصولی را از سبد خرید حذف کنید.`;
                }

                return response;
            }

            // If we get here, the cart is empty
            const currentLanguage = getCurrentLanguage();
            return currentLanguage === LANGUAGE.ENGLISH
                ? "Your shopping cart is empty."
                : "سبد خرید شما خالی است.";
        } catch (error) {
            console.error("Error getting cart info:", error);
            const currentLanguage = getCurrentLanguage();
            return currentLanguage === LANGUAGE.ENGLISH
                ? "Sorry, there was a problem retrieving your cart information. Please try again."
                : "متاسفانه در دریافت اطلاعات سبد خرید مشکلی پیش آمد.";
        }
    }
}
