// --- VECTOR STORE CONSTANTS ---
export enum VECTOR_INDEX_NAME {
    FAQS = "faqs",
    INVENTORIES = "inventories",
}

export enum VECTOR_TOKEN_NAME {
    INVENTORY_VECTOR_STORE = "inventory-vector-store",
    FAQ_VECTOR_STORE = "faq-vector-store",
}

export enum VECTOR_STORE_PROVIDER {
    ELASTIC_SEARCH = "elasticsearch",
}

// --- CHATBOT MESSAGE TYPES ---
export enum CHATBOT_MESSAGE_TYPES {
    USER = "user",
    SYSTEM = "system",
    AI = "ai",
}

export type ChatBoMessageResponse = {
    text: string;
    type: CHATBOT_MESSAGE_TYPES;
};

// --- CART ACTIONS ---
export enum CART_ACTION {
    ADD = "add",
    REMOVE = "remove",
}

// --- WORKFLOW STATES ---
export enum WORKFLOW_NODE {
    INIT = "init",
    AGENT = "agent",
    TOOLS = "tools",
    SUMMARIZER = "summarizer",
}

// --- INSTAGRAM RESPONSES TYPE ---
export enum INSTAGRAM_RESPONSE_TYPE {
    AUTOMATION = "automation",
    CHATBOT = "chatbot",
    INVENTORY = "inventory",
    CART = "cart",
}

// --- INSTAGRAM RESPONSE STRUCTURE ---
export type InstagramResponse = {
    type: INSTAGRAM_RESPONSE_TYPE;
    messages: {
        text: string;
        payload?: Record<string, unknown>;
    }[];
    args?: string;
};

// --- TOOL CONSTANTS ---
export enum TOOL_CALLS_KEY {
    TOOL_CALLS = "tool_calls",
}

// --- JOBS ---
export enum JOB_EVENTS {
    SUBSCRIBE_TO_EVENTS = "subscribe-to-events",
}

// --- MODEL PROVIDERS ---
export enum CHAT_MODEL {
    OPENAI = "openai",
    VERTEX = "vertex",
}

export enum EMBEDDING_MODEL {
    OPENAI_EMBEDDING = "openai-embedding",
}

// --- MODEL CONFIGURATION ---
export enum MODEL_CONFIG {
    OPENAI_MODEL_GPT4O = "gpt-4o",
    GEMINI_MODEL = "gemini-1.5-pro-002",
    OPENAI_EMBEDDING_MODEL = "text-embedding-3-small",
}

// --- USER REGISTRATION METHODS ---
export enum REGISTRATION_METHOD {
    PHONE = "phone",
    GOOGLE = "google",
}

export type ProductDetails = {
    name: string;
    price: string;
    attributes: Array<{ key: string; value: string }>;
    total?: number;
    availableStock?: number;
};

export type CardHelperFunctions = {
    sendInventoryImage: (id: string) => Promise<boolean>;
    manageCart: (
        id: string,
        action: CART_ACTION,
        quantity: number,
    ) => Promise<string>;
    getCartsInfo: () => Promise<string>;
    getProductDetails?: (id: string) => Promise<ProductDetails | null>;
};

export interface CartItem {
    id: string;
    amount: number;
    name?: string;
    price?: string;
    attributes?: Array<{ key: string; value: string }>;
}

export interface Cart {
    items: CartItem[];
    pendingConfirmation: boolean;
}

export enum TABLE_NAMES {
    LLM_CHAT_HISTORY = "llm_chat_history",
}

/**
 * Supported application languages enum
 * ENGLISH for English, PERSIAN for Persian/Farsi
 */
export enum LANGUAGE {
    ENGLISH = "en",
    PERSIAN = "fa",
}

export type AppLanguage = LANGUAGE.ENGLISH | LANGUAGE.PERSIAN;
