import { injectable } from "tsyringe";
import C<PERSON><PERSON><PERSON>o from "./client.repo";

import { AddClientDto, EditClientDto } from "./types";

import { getClientSerializer, getClientsSerializer } from "./responses";
import { errors, utils } from "../../../common";
import { detectTimePeriod, generateDateRange } from "../../../common/lib/utils/dates";
import { EntityManager } from "typeorm";
import Logger from "../../../common/lib/metrics/logger";
import {
    clientAddLog,
    clientEditLog,
    clientGetLog,
    clientNotFoundErrorLog,
    recordErrorValue,
} from "../../../common/lib/metrics/metrics";

@injectable()
export default class ClientService {
    constructor(private _repo: ClientRepo) {}

    async getClientReport(query: Partial<Express.Query>, userId: number) {
        const clients = await this._repo.getClientsForAdmin(userId, query);

        return getClientsSerializer(clients);
    }

    async addClient(args: Add<PERSON><PERSON><PERSON><PERSON>, manager: EntityManager) {
        await this._repo.upsert(args, ["channelId", "platformId"], { manager });

        const client = await this._repo.findOneByQuery(
            {
                channelId: args.channelId,
                platformId: args.platformId,
            },
            [],
            { manager },
        );

        clientAddLog.inc();
        Logger.info("Client successfully added", { client });

        return client;
    }

    async getClientOfChannelByPlatformId(
        channelId: number,
        platformId: string,
    ) {
        const client = await this._repo.findOneByQuery({
            channelId,
            platformId,
        });

        clientGetLog.inc();
        Logger.info("Client successfully retrieved", { client });

        return client;
    }

    async getClients(query: Partial<Express.Query>, profile: Express.User) {
        const { id: userId } = profile;

        const clients = await this._repo.getClientsForUser(query, userId);

        clientGetLog.inc();
        Logger.info("Client successfully retrieved by userId", { clients });

        return getClientsSerializer(clients);
    }

    async getClient(clientId: number, profile: Express.User) {
        const { id: userId } = profile;

        const client = await this._repo.getClientForUser(clientId, userId);

        if (client.length === 0) {
            Logger.error("Client not found", { clientId, userId });
            clientNotFoundErrorLog.inc();
            recordErrorValue("NotFoundError", "Client not found");

            throw new errors.NotFoundError("Client");
        }

        clientGetLog.inc();
        Logger.info("Client successfully retrieved by userId", { client });

        return getClientSerializer(client);
    }

    async getClientOfUser(clientId: number, userId: number) {
        const client = await this._repo.findOneByQuery({
            userId,
            id: clientId,
        });

        if (utils.isNil(client)) {
            clientNotFoundErrorLog.inc();

            Logger.error("Client not found", {
                statusCode: 404,
                clientId,
            });

            recordErrorValue("not found error", "Client not found");
            throw new errors.NotFoundError("Client");
        }

        clientGetLog.inc();
        Logger.info("Client successfully retrieved by userId", { client });

        return client;
    }

    async editClient(id: number, args: EditClientDto, profile: Express.User) {
        const { id: userId } = profile;

        const isUpdated = await this._repo.updateOneByQuery(
            { id, userId },
            args,
        );

        if (!isUpdated) {
            Logger.error("Client not found for edit", { id, userId });
            clientNotFoundErrorLog.inc();
            recordErrorValue("NotFoundError", "Client not found for edit");

            throw new errors.NotFoundError("Client");
        }

        clientEditLog.inc();
        Logger.info("Client successfully updated", { id, userId });
    }

    getClientsCount = async (
        parsedQuery: Partial<Express.Query>,
        profile: Express.User,
    ) => {
        const { id: userId } = profile;

        if (utils.isNil(parsedQuery.filter)) {
            Logger.error("Invalid query: filter is missing");

            throw new errors.BadRequestError();
        }

        const { from, to } = parsedQuery.filter;

        if (utils.isNil(from)) {
            Logger.error("Invalid query: 'from' or 'to' is missing");

            throw new errors.BadRequestError();
        }
        if (utils.isNil(to)) {
            Logger.error("Invalid query: 'from' or 'to' is missing");
            throw new errors.BadRequestError();
        }

        // Parse the dates
        const fromDate = new Date(from);
        const toDate = new Date(to);

        // Check if the dates are valid
        if (isNaN(fromDate.getTime()) || isNaN(toDate.getTime())) {
            Logger.error("Invalid query: invalid date format", {
                userId,
                statusCode: 400,
                error: "badRequestError",
                from,
                to,
            });

            throw new errors.BadRequestError();
        }

        // Detect time period and adjust date range if needed
        const timePeriod = detectTimePeriod(fromDate, toDate);
        let adjustedFrom = from;
        let adjustedTo = to;

        if (timePeriod) {
            // If a specific time period is detected, adjust the date range
            // Use toDate as the reference date (today)
            const dateRange = generateDateRange(toDate, timePeriod);

            // Format dates to ISO string
            adjustedFrom = dateRange.from.toISOString();
            adjustedTo = dateRange.to.toISOString();

            Logger.info(`Detected time period: ${timePeriod}, adjusting date range`, {
                userId,
                originalFrom: from,
                originalTo: to,
                adjustedFrom,
                adjustedTo,
            });
        }

        const [data] = await this._repo.getClientsCount(userId, adjustedFrom, adjustedTo);

        Logger.info("Client count retrieved", { data });

        return {
            data,
            metadata: {
                from: adjustedFrom,
                to: adjustedTo,
            },
        };
    };
}
