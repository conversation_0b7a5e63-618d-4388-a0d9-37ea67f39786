import express from "express";
import { container } from "tsyringe";
import clientController from "./client.controller";

import * as schemas from "./schemas";
import { middlewares } from "../../../common";

const router = express.Router();

const controller = container.resolve(clientController);

router.route("/").get(middlewares.JWT, controller.getClients);

router.route("/report").get(middlewares.JWT, controller.getClientsCount);

router
    .route("/:clientId")
    .get(middlewares.JWT, controller.getClient)
    .patch(middlewares.JWT, schemas.editClient, controller.editClient);

export default router;
