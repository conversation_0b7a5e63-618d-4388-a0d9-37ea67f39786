version: "3.9"

services:
    db:
        container_name: palette-db-us
        image: docker.arvancloud.ir/postgres
        restart: always
        volumes:
            - db_us:/var/lib/postgresql/data
        environment:
            - POSTGRES_DB=${POSTGRES_DB_NAME}
            - POSTGRES_USER=${POSTGRES_DB_USER}
            - POSTGRES_PASSWORD=${POSTGRES_DB_PASSWORD}
        ports:
            - "5433:5432"
        networks:
            - us_net

    cache:
        container_name: palette-cache-us
        image: docker.arvancloud.ir/redis
        restart: always
        volumes:
            - cache_us:/data
        environment:
            - ALLOW_EMPTY_PASSWORD=yes
        networks:
            - us_net

    elasticsearch:
        container_name: elasticsearch-us
        image: docker.arvancloud.ir/elasticsearch:8.15.3
        restart: always
        volumes:
            - data_us:/usr/share/elasticsearch/data
        environment:
            - discovery.type=single-node
            - xpack.security.enabled=false
            - ES_JAVA_OPTS=-Xms1g -Xmx2g
        networks:
            - us_net

    kibana:
        container_name: kibana-us
        image: docker.elastic.co/kibana/kibana:8.5.1
        restart: always
        ports:
            - "5602:5601"
        environment:
            - ELASTICSEARCH_URL=http://elasticsearch-us:9200
        depends_on:
            - elasticsearch
        networks:
            - us_net

    api:
        container_name: palette-api-us
        image: palette/api
        build:
            context: .
            dockerfile: Dockerfile
        restart: always
        depends_on:
            - db
            - cache
            - elasticsearch
        ports:
            - "3001:3001"
        volumes:
            - /etc/palette/us:/etc/palette/us
        command: ["sh", "-c", "yarn migration:run-prod && yarn start"]
        environment:
            - ENV_NAME=${ENV_NAME}
            - NODE_ENV=${NODE_ENV}
            - PROJECT=${PROJECT}
            - DOMAIN=${DOMAIN}
            - PORT=${PORT}
            - WEB_APP_URL=${WEB_APP_URL}
            - VIRTUAL_HOST=${VIRTUAL_HOST}
            - LETSENCRYPT_HOST=${LETSENCRYPT_HOST}
            - VIRTUAL_PORT=${VIRTUAL_PORT}
            - ENABLE_CONSOLE_LOG=${ENABLE_CONSOLE_LOG}
            - ENABLE_PROMETHEUS_LOG=${ENABLE_PROMETHEUS_LOG}
            - LOG_FILE_PATH=${LOG_FILE_PATH}
            - POSTGRES_DB_HOST=${POSTGRES_DB_HOST}
            - POSTGRES_DB_PORT=${POSTGRES_DB_PORT}
            - POSTGRES_DB_NAME=${POSTGRES_DB_NAME}
            - POSTGRES_DB_USER=${POSTGRES_DB_USER}
            - POSTGRES_DB_PASSWORD=${POSTGRES_DB_PASSWORD}
            - OPENAI_API_KEY=${OPENAI_API_KEY}
            - CHATBOT=${CHATBOT}
            - EMBEDDING=${EMBEDDING}
            - ELASTIC_URI=${ELASTIC_URI}
            - SALT_ROUNDS=${SALT_ROUNDS}
            - SESSION_SECRET=${SESSION_SECRET}
            - JWT_SECRET=${JWT_SECRET}
            - REDIS_HOST=${REDIS_HOST}
            - REDIS_PORT=${REDIS_PORT}
            - REDIS_PREFIX=${REDIS_PREFIX}
            - FILER_URL=${FILER_URL}
            - ZIBAL_MERCHANT_ID=${ZIBAL_MERCHANT_ID}
            - ZIBAL_PREFIX_URL=${ZIBAL_PREFIX_URL}
            - ZIBAL_CALL_BACK_URL=${ZIBAL_CALL_BACK_URL}
            - PAYMENT_LINK_PREFIX_URL=${PAYMENT_LINK_PREFIX_URL}
            - ZIBAL_KEY=${ZIBAL_KEY}
            - NAVASAN_KEY=${NAVASAN_KEY}
            - KAVENEGAR_API_KEY=${KAVENEGAR_API_KEY}
            - INSTAGRAM_CLIENT_ID=${INSTAGRAM_CLIENT_ID}
            - INSTAGRAM_CLIENT_SECRET=${INSTAGRAM_CLIENT_SECRET}
            - INSTAGRAM_CALLBACK_URL=${INSTAGRAM_CALLBACK_URL}
            - INSTAGRAM_WEBHOOK_URL=${INSTAGRAM_WEBHOOK_URL}
            - INSTAGRAM_WEBHOOK_TOKEN=${INSTAGRAM_WEBHOOK_TOKEN}
            - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
            - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
            - GOOGLE_USERINFO_URL=${GOOGLE_USERINFO_URL}
            - GOOGLE_APPLICATION_CREDENTIALS=${GOOGLE_APPLICATION_CREDENTIALS}
            - GOOGLE_CLOUD_PROJECT=${GOOGLE_CLOUD_PROJECT}
            - STATIC_SERVER_URL=${STATIC_SERVER_URL}
            - PUBLIC_APP_LANG=${PUBLIC_APP_LANG}
            - FIX_USER=${FIX_USER}
            - FIX_PASS=${FIX_PASS}

        networks:
            - us_net

    seaweed_master_us:
        container_name: palette-seaweedfs-master-us
        image: docker.arvancloud.ir/chrislusf/seaweedfs
        command: "master -ip=seaweed_master_us -ip.bind=0.0.0.0 -metricsPort=9324"
        networks:
            - us_net

    seaweed_volume_us:
        container_name: palette-seaweedfs-volume-us
        image: docker.arvancloud.ir/chrislusf/seaweedfs
        volumes:
            - files_us:/data
        command: 'volume -mserver="seaweed_master_us:9333" -ip.bind=0.0.0.0 -port=8080 -metricsPort=9325'
        depends_on:
            - seaweed_master_us
        networks:
            - us_net

    seaweed_filer_us:
        container_name: palette-seaweedfs-filer-us
        image: docker.arvancloud.ir/chrislusf/seaweedfs
        volumes:
            - files_us:/data
        command: 'filer -master="seaweed_master_us:9333" -ip.bind=0.0.0.0 -metricsPort=9326'
        depends_on:
            - seaweed_master_us
            - seaweed_volume_us
        networks:
            - us_net

networks:
    us_net:
        external: true
        name: nginx-proxy-us

volumes:
    db_us:
    cache_us:
    files_us:
    data_us:
